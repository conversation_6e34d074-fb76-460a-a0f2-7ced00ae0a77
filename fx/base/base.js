import { FxElement, html } from '/fx.js';
import { $styles, $btnsMT, $icons } from './base.x.js';
import { BS_ITEM } from './src/base-item.js';
import '../main/main.js';
import '../dbs/dbs.js';
import '../tree/tree.js';
import '../table/table.js';
import '../jupyter/jupyter.js';
import '../desk/desk.js';

export class FxBase extends FxElement {
    static properties = {
        showLoader: { type: Boolean, default: false },
        fxItems: { type: Object },
        dbPrefix: { type: String, default: 'a$_' },
        fxId: { type: String, default: '$fx-tree:000000' },
        fxSelected: { type: Object },
        // bs - BS_ITEM
        bsItems: { type: Array },
        bsFlat: { type: Object, default: {} },
        bsAllPersons: { type: Object },
        bsSelected: { type: Object, $notify: true },
        bsType: { type: String, default: 'item' },
        // any - item
        toDeleteKeys: { type: Array, default: [] },
        toSaveAttachments: { type: Object },
        notebook: { type: Object },
        addNotebook: { type: Object },
        isItem: { type: Object },
        changesMap: { type: Object },
        calendarData: { type: Object },
        tableItems: { type: Object },
        tablePersons: { type: Object },
        hideDBS: { type: Boolean, default: true },
        lastLZS: { type: String, default: '', save: true }
    }
    get main() { return this.$qs('fx-main') }
    get tree() { return this.$qs('fx-tree') }
    get fxAll() { return BS_UTILS.allItems(this.fxItems) || [] }
    get fxFlat() { return BS_UTILS.flatItems(this.fxItems, true) || {} }
    get fxFlatLength() { return Object.keys(this.fxFlat || {}).length }
    get fxToDelete() { return this.fxAll?.filter(i => i._deleted) }
    get fxHasDeleted() { return this.fxToDelete.length || this.toDeleteKeys?.length || Object.keys(this.toSaveAttachments || {}).length }

    get changesMap() { return BS_ITEM.changesMap }
    get needSave() { return this.changesMap?.size > 0 || this.fxHasDeleted || this.fxChanged || this.attachments?.length }

    get dbs() { return this.$qs('fx-dbs') }

    get tabs() {
        let tabs = {
            id: this.id + '-tabs', hideClose: true, noDraggable: true,
            tabs: [
                {
                    id: 'desktop', icon: 'twemoji:desktop-computer', title: 'desktop',
                },
                {
                    id: 'info', icon: 'twemoji:information', title: 'info',
                },
            ]
        }
        if (this.fxSelected?.hideInfo) {
            tabs.tabs.pop();
        }
        if (this.fxSelected?.is === 'money') {
            import('../jmoney/jmoney.js');
            tabs.tabs.push({ id: 'money', icon: 'flat-color-icons:currency-exchange', title: 'money', group: 'money' });
        }
        if (this.fxSelected?.is === 'moneyTotal') {
            import('../jmoney/jmoney.js');
            tabs.tabs.push({ id: 'money-total', icon: 'noto-v1:money-bag', title: 'money total', width: 110, group: 'money-total' });
        }
        if (this.fxSelected?.is === 'man' || this.fxSelected?.is === 'woman') {
            import('../persona/persona.js');
            import('../family-tree/family-tree.js');
            tabs.tabs.push({ id: 'persona', icon: 'carbon:pedestrian-family', title: 'persona', group: 'persona' });
            tabs.tabs.push({ id: 'family-tree', icon: 'game-icons:family-tree', title: 'family tree', group: 'persona' });
        }
        if (this.fxSelected?.is === 'hlsDay') {
            import('../hls-day/hls-day.js');
            tabs.tabs.push({ id: 'hls-day', icon: 'flat-color-icons:sports-mode', title: 'hls day', group: 'hls' });
        }
        if (this.fxSelected?.is === 'hlsTotal') {
            import('../hls-total/hls-total.js');
            tabs.tabs.push({ id: 'hls-total', icon: 'fc-combo_chart', title: 'hls total', group: 'hls' });
        }
        if (this.fxSelected?.if?.ifEvents) {
            import('../events/events.js');
            tabs.tabs.push({ id: 'events', icon: 'flat-color-icons:overtime', title: 'events', group: 'events' });
        }
        if (this.fxSelected?.if?.ifCalendar) {
            import('../calendar/calendar.js');
            tabs.tabs.push({ id: 'calendar', icon: 'flat-color-icons:planner', title: 'calendar', group: 'calendar' });
        }
        if (this.addNotebook) {
            tabs.tabs.push({ id: 'add-info', icon: 'emojione-v1:circled-information-source', title: 'add-info', group: 'events', ShowEvery: true });
        }
        return tabs;
    }
    get shortLabel() { return this.fxSelected?.label.slice(0, 42) + (this.fxSelected?.label.length > 42 ? '...' : '') }
    get prefixid() { return this.dbLocal?.name.replace('$', '') }

    constructor() {
        super();
        const separator = window.location.hash || window.location.search;
        this.hashParams = new URLSearchParams(separator.substring(1));
        const prefix = this.hashParams.get('pr')
        this.dbPrefix = localStorage.getItem('fx-base.dbPrefix');
        if (prefix)
            this.dbPrefix = prefix;
        this.dbPrefix ||= 'a$_';
        localStorage.setItem('fx-base.dbPrefix', this.dbPrefix);
        this.id = this.dbPrefix.replaceAll('$', '') + (this.id || 'fx-base');
        // console.log(this.id);
    }
    firstUpdated() {
        super.firstUpdated();
        this.async(async () => {
            FX.fxBase = this;
            let lzs = this.hashParams.get('lzs');
            if (lzs) {
                this.style.opacity = 1;
                this.showLoader = true;
                await this.getLZS(lzs);
            } else {
                this.hideDBS = false;
            }
            this.main._btnClick = this.on_btnClick.bind(this);
            this.async(() => {
                this.listen('tab-closed', async (e) => {
                    const tab = e?.detail.tab;
                    if (tab.id === 'add-info')
                        this.addNotebook = undefined;
                    this.$update();
                })
                this.initTableItems();
                this.initTablePersons();
                this.style.opacity = 1;
                this.$update();
                this.listen('desk-item-added', (e) => {
                    this.fxChanged = this._tmpFxChanged;
                    this.$update();
                })
            }, 500)
        })
    }

    async on_dbSelected(e) {
        if (e?.detail?.dbLocal)
            this.dbLocal = e.detail.dbLocal;
        await this.checkDb();
        try {
            this.fxItems = await this.dbLocal.get(this.fxId)
        } catch (err) { console.log(err) }
        this.async(async () => {
            await this.getBsItem();
            this.initTableItems();
            this.initTablePersons();
            this.$update();
        }, 20)
    }
    async checkDb(db = this.dbLocal) {
        let tree;
        try {
            tree = await db.get(this.fxId);
        } catch (err) { }
        if (!tree) {
            tree = {
                _id: this.fxId,
                expanded: true,
                label: db.name,
                date_utc: FX.dates().utc,
                date_local: FX.dates().local,
                items: []
            }
            await db.put(tree);
        }
    }
    async getBsItem(_id = this.fxSelected?._id, onlyGet = false, sure = false) {
        if (!_id || !this.fxFlatLength) return;
        let item = this.bsFlat[_id];
        if (!item || sure) {
            const doc = this.fxFlat[_id];
            item = new BS_ITEM({ doc, isLoad: true });
            this.bsFlat ||= {};
            this.bsFlat[_id] = item;
        }
        if (!onlyGet) {
            this.bsSelected = item;
            await this.getInfo();
            await this.getIs();
            await this.getIf();
            this.$update();
        }
        return item;
    }
    async on_itemSelected(e) {
        const item = e.detail?.item || this.fxFlat?.[e.detail?.row?._id];
        this.fxSelected = item;
        this.async(() => {
            if (this.isShowTreeSets)
                this.$fire('on_itemSelected', { item: this.dataTreeSets(), label: this.shortLabel, type: 'tree-sets' });
            if (this.isShowAddItem)
                this.$fire('on_itemSelected', { item: this.dataAddItem(), label: this.shortLabel, type: 'add-item' });

        }, 20)
        await this.getBsItem();
        this.async(async () => {
            let is = item?.is || 'info';
            if (is === 'man' || is === 'woman') {
                is = 'persona';
                await this.getParents();
                await this.getSpouses();
                await this.getChildren();
            }
            else if (item?.if?.ifEvents) is = 'events';
            else if (item?.if?.ifCalendar) is = 'calendar';
            is = is === 'hlsDay' ? 'hls-day' : is;
            is = is === 'hlsTotal' ? 'hls-total' : is;
            is = is === 'moneyTotal' ? 'money-total' : is;
            // this.main.fxTabs.selectTab('', is);
            if (is === 'calendar') this.fire('select-item-calendar', { item });
        }, 100)
    }
    async getBsFlat(is) {
        const keys = [];
        this.fxAll.map(i => {
            if (!is)
                keys.add(i._id);
            else if (is === i.is || is.includes(i.is))
                keys.add(i._id);
        })
        return await this.getBsFlatByKeys(keys);
    }
    async getBsFlatByKeys(keys) {
        if (!keys || !this.dbLocal) return;
        const allDocs = await this.dbLocal.allDocs({ keys, include_docs: true });
        let flat = {};
        allDocs.rows.map(i => {
            if (i.doc)
                flat[i.id] = new BS_ITEM({ doc: i.doc });
        })
        return flat;
    }
    async on_addItem(e, res) {
        const selected = e?.detail?.selected || e || this.fxSelected;
        let setItems = false,
            newItem = new BS_ITEM({ label: '', type: 'item', parentId: selected._id });
        if (res?.type) {
            newItem.icon = res.icon;
            if (res.is)
                newItem.is = res.is;
            if (!res.inside && selected._id !== this.fxId) {
                newItem.parentId = selected.parentId;
                (this.fxFlat[newItem.parentId] || this.fxItems).items.push(newItem.doc);
                setItems = true;
            }
        } else {
            if (selected.if?.ifCalendar || selected.is === 'man' || selected.is === 'woman' || res) {
                if (!res) {
                    await this.showAddItem(e, selected);
                    res = {};
                    return;
                }
                newItem.icon = res.icon;
                if (res.value === 'calendar') {
                    newItem.if ||= {};
                    newItem.if.ifCalendar = true;
                } else if (res.value === 'man' || res.value === 'woman') {
                    newItem.is = res.value;
                }
                if (!res.inside && selected._id !== this.fxId) {
                    newItem.parentId = selected.parentId;
                    (this.fxFlat[newItem.parentId] || this.fxItems).items.push(newItem.doc);
                    setItems = true;
                }
            } else {
                if (selected.is === 'money' || selected.is === 'moneyTotal') {
                    newItem.is = 'money';
                    newItem.icon = 'fc:currency-exchange';
                    newItem.label = FX.dates().monthStr;
                    if (selected.is === 'money') {
                        newItem.parentId = selected.parentId;
                        this.fxFlat[newItem.parentId].items.push(newItem.doc);
                        setItems = true;
                    }
                } else if (selected.is === 'hlsDay' || selected.is === 'hlsTotal') {
                    newItem.is = 'hlsDay';
                    newItem.icon = 'fc:sports-mode';
                    newItem.label = FX.dates().short;
                    if (selected.is === 'hlsDay') {
                        newItem.parentId = selected.parentId;
                        this.fxFlat[newItem.parentId].items.push(newItem.doc);
                        setItems = true;
                    }
                }
            }
        }
        if (res && !res?.value && !res?.type) return;
        if (res?.if) newItem.if = res.if;
        if (!setItems) {
            selected.items ||= [];
            selected.items.push(newItem.doc);
        }
        this.fxFlat ||= {};
        this.fxFlat[newItem._id] = newItem.doc;
        this.bsFlat[newItem.id] = newItem;
        selected.expanded = true;
        this.fxChanged = true;
        if (res && !res.noAddInfo)
            this.addInfo(newItem, selected._id, res);
        this.$update();
        this.async(() => {
            this.$update();
            this.main.$update();
        }, 20)
        return newItem.doc;
    }
    addInfo(newItem, parentId, res) {
        const _id = 'info:' + newItem._id;
        let doc = {
            _id,
            ulid: FX.ulid(),
            type: 'info',
            cells: [
                {
                    ulid: FX.ulid(),
                    type: 'jupyter_cell',
                    cell_type: res?.type || 'link',
                    cell_extType: res?.type || 'link',
                }
            ]
        }
        const bs = new BS_ITEM({ _id, parentId, type: 'info', doc });
        this.bsFlat ||= {};
        this.bsFlat[_id] = bs;
        if (!res) {
            this.notebook = bs.doc;
            this.notebook.$label = newItem.label;
            this.notebook.icon = newItem.icon;
            this.tree.selectById(newItem._id);
            this.async(() => {
                if (this.$qs('fx-jupyter')?.fxCells?.[0])
                    this.$qs('fx-jupyter').fxCells[0].toolbarVisible = res ? false : true;
            }, 300)
        }
        this.$update();
        this.async(() => {
            this.$update();
            this.main.$update();
        }, 20)
    }
    treeChanged(e) {
        this._tmpFxChanged = this.fxChanged;
        this.async(() => {
            this.fxChanged = true;
            this.main.$update();
        })
    }

    async createNotebook(selected = this.fxSelected, db = this.dbLocal, nb = 'notebook') {
        if (!selected) return;
        const _id = 'info:' + selected._id;
        let itemInfo = BS_ITEM.changesMap.get(_id) || this.bsFlat[_id];
        if (!itemInfo) {
            let doc,
                _doc = {
                    parentId: selected._id,
                    label: '', 
                    cells: [],
                    labelSize: '16px',
                    labelBold: '100',
                    getInfo: true
                }
            try { doc = await db.get(_id) } catch (err) { }
            if (doc)
                itemInfo = new BS_ITEM({ _id, type: 'info', doc });
            else
                itemInfo = new BS_ITEM({ _id, type: 'info', _doc });
        }
        this.bsFlat ||= {};
        this.bsFlat[_id] = itemInfo;
        this[nb] = itemInfo.doc;
        this[nb].$label = selected.label;
        this[nb].icon = selected.icon;
        this.$update();
        return itemInfo;
    }
    async getInfo(selected = this.fxSelected, db = this.dbLocal, nb = 'notebook') {
        if (!selected) return;
        const _id = 'info:' + selected._id;
        let itemInfo = BS_ITEM.changesMap.get(_id) || this.bsFlat[_id];
        if (!itemInfo) {
            let doc;
            try { doc = await db.get(_id) } catch (err) { }
            if (doc) 
                itemInfo = new BS_ITEM({ _id, type: 'info', isLoad: true, doc });
            else
                itemInfo = null; 
        }
        if (!itemInfo) {
            this.notebook = null;
            return;
        }
        this.bsFlat ||= {};
        this.bsFlat[_id] = itemInfo;
        this[nb] = itemInfo.doc;
        this[nb].$label = selected.label;
        this[nb].icon = selected.icon;
        this.$update();
        return itemInfo;
    }
    async getIs(selected = this.fxSelected, db = this.dbLocal) {
        if (!selected) return;
        let type = selected?.is;
        // console.log('itemIs - ', type);
        if (!type) return;
        let _id = type + ':' + selected._id,
            item = BS_ITEM.changesMap.get(_id);
        if (!item) {
            let doc;
            try { doc = await db.get(_id) } catch (err) { }
            item = new BS_ITEM({ _id, type, isLoad: true, doc: (doc || { parentId: selected._id }) });
        }
        this.isItem = item.doc;
        this.$update();
    }
    async getIf(selected = this.fxSelected, db = this.dbLocal) {
        if (!selected) return;
        let typeIf = selected.if;
        // console.log('itemIf - ', typeIf);
        if (!typeIf) return;

    }

    initTableItems() {
        this.tableItems = { columns: [], data: [], calc: {} };
        let tableItems = {};
        tableItems.columns = [
            { header: 'label', field: 'name', width: 'auto', textAlign: 'left', alignItems: 'flex-start', showTitle: true, class: 'fs', style: 'text-wrap: auto;', footerClass: 'fs' },
            { header: 'tags', field: 'tags', width: 70, typeColumn: 'html', class: 'fxs', style: 'text-wrap: auto;' },
            { header: 'date', field: 'date', width: 66, textAlign: 'center', class: 'fxs', style: 'text-wrap: auto;' }
        ]
        let rows = [];
        Object.values(this.fxFlat || {}).map(i => {
            if (!i.hideInfo && i.is !== 'man' && i.is !== 'woman') {
                const row = {}, doc = i;
                row.date = doc.date_local;
                row.name = doc.label;
                // row.tags = '#r' + (doc.rating || 0) + ' ' + (doc.tags || '');
                row.tags = `<span class="fxs hidden">${'#r' + (doc.rating || 0) + ' '}</span><span class="fxs">${doc.tags || ''}</span>`;
                row._id = doc._id;
                rows.push(row)
            }
        })
        rows.sort((a, b) => a.date < b.date ? 1 : -1);
        tableItems.data = rows;
        // tableItems.calc = { name: { type: 'count' } };
        this.tableItems = tableItems;
        this.$update();
        this.async(() => {
            this.$qs('#' + this.prefixid + '-items')?.updateVisibleRows();
        }, 100)
    }
    async initTablePersons() {
        this.tablePersons = { columns: [], data: [], calc: {} };
        let tablePersons = {};
        tablePersons.columns = [
            { header: 'persona', field: 'name', width: 'auto', textAlign: 'left', alignItems: 'flex-start', showTitle: true, class: 'fs', style: 'text-wrap: auto;', footerClass: 'fs' },
            { header: 'dateS', field: 'date1', width: 66, textAlign: 'left', class: 'fxs', style: 'text-wrap: auto;' },
            { header: 'dateE', field: 'date2', width: 66, textAlign: 'left', class: 'fxs', style: 'text-wrap: auto;' },
        ]
        let rows = [];
        let keys = [];
        Object.values(this.fxFlat || {}).map(i => {
            if (i.is === 'man' || i.is === 'woman') {
                keys.push(i._id);
                const row = {}, doc = i;
                row.dates = doc.date1 + ' ' + (doc.date2 || '');
                row.name = doc.label;
                row._id = doc._id;
                rows.push(row)
            }
        })
        let all = await this.getBsFlatByKeys(keys);
        rows.map(i => {
            i.date1 = all[i._id]?.doc?.dateStart?.split('T')[0] || '';
            i.date2 = all[i._id]?.doc?.dateEnd?.split('T')[0] || '';
        })
        rows.sort((a, b) => a.name > b.name ? 1 : -1);
        tablePersons.data = rows;
        // tablePersons.calc = { name: { type: 'count' } };
        this.tablePersons = tablePersons;
        this.$update();
        this.async(() => {
            this.$qs('#' + this.prefixid + '-persons')?.updateVisibleRows();
        }, 100)
    }

    treeRowDeleted(e) {
        this.toDeleteKeys ||= [];
        this.toDeleteKeys.push(...e.detail.deletedItems.map(i => i._id || i));
        this.$update();
    }

    async on_btnClick(e, id) {
        id ||= e.target?.id;
        // console.log(id);
        if (id === 'search') {
            this.initTableItems();
            this.$update();
            return;
        }
        if (id === 'persons') {
            this.initTablePersons();
            this.$update();
            return;
        }
        if (id === 'btn-refresh') {
            await this.tree?.storeTreeState(null, true);
            this.async(() => {
                document.location.reload();
            }, 100)
            return;
        }
        if (id === 'btn-settings') {
            console.log(id);
            return;
        }
        if (id === 'btn-tree-sets') {
            this.showSets(e);
        }
        if (id === 'btn-save') {
            this.save();
        }
    }

    static styles = [$styles]

    get leftPanel() {
        let opt = { footerStyle: 'border: none;' };
        let fxItems = this.fxItems?.items?.[0]?._id === '$root-item:000000' ? this.fxItems?.items?.[0] : this.fxItems;
        return html`
            <div slot="files" class="vertical flex overflow-h h100" style="background: var(--fx-background)">
                <fx-tree id=${this.prefixid + '-fxtree'} .item=${fxItems} .base=${this} @selected=${this.on_itemSelected} @add-item=${e => this.on_addItem(e)} @changed=${this.treeChanged} @removed-deleted=${this.treeRowDeleted} remoteAddItem>
                    <div class="horizontal flex" slot="panel">
                        <div class="flex"></div>
                        <fx-icon id="btn-tree-sets" class="but ml8" url="mdi:dots-vertical" scale=.8 an="btn" br="square" @click=${this.on_btnClick}></fx-icon>
                    </div>    
                </fx-tree>
            </div>
            <div slot="search" class="vertical flex overflow-h h100" style="background: var(--fx-background)">
                <fx-table .id=${this.prefixid + '-items'} rowHeight=48
                    .columns=${this.tableItems?.columns} 
                    .data=${this.tableItems?.data || []} 
                    @row-selected=${this.on_itemSelected}
                    footerHeight="1" .options=${opt}
                ></fx-table>
            </div>
            <div slot="persons" class="vertical flex overflow-h h100" style="background: var(--fx-background)">
                <fx-table .id=${this.prefixid + '-persons'} rowHeight=48
                    .columns=${this.tablePersons?.columns} 
                    .data=${this.tablePersons?.data || []} 
                    @row-selected=${this.on_itemSelected}
                    footerHeight="1" .options=${opt}
                ></fx-table>
            </div>
        `
    }
    get rightPanel() {
        if (!this.hideDBS)
            return html`
                <div slot="right" class="vertical flex overflow-y h100" style="background: var(--fx-background)">
                    <fx-dbs .base=${this} @db-selected=${this.on_dbSelected}></fx-dbs>
                </div>
            `
    }
    get mainPanel() {
        return html`
            <fx-desk id=${this.prefixid + '-desk'} slot="desktop"></fx-desk>
            <fx-jupyter slot="info" class="vertical flex overflow h100" .notebook=${this.notebook} .base=${this} showBorder></fx-jupyter>
            ${this.main?.activeTabGroup === 'money' ? html`
                <fx-jmoney slot="money" class="vertical flex overflow-h h100" .cell=${this.isItem} .base=${this}></fx-jmoney>
            ` : ''}
            ${this.main?.activeTabGroup === 'money-total' ? html`
                <fx-jmoney slot="money-total" class="vertical flex overflow-h h100" .cell=${this.isItem} isTotal .base=${this}></fx-jmoney>
            ` : ''}
            ${this.main?.activeTabGroup === 'calendar' ? html`
                <fx-calendar slot="calendar" class="vertical flex overflow-h h100" start=0 end=0 simpleMode .base=${this}></fx-calendar>
            ` : ''}
            ${this.main?.activeTabGroup === 'persona' ? html`
                <fx-persona slot="persona" class="vertical flex overflow-h h100" .item=${this.bsSelected} .base=${this}></fx-persona>
                <fx-family-tree slot="family-tree" class="vertical flex overflow-h h100" .base=${this} .bsSelected=${this.bsSelected} .bsAllPersons=${this.bsAllPersons}></fx-family-tree>
            ` : ''}
            ${this.main?.activeTabGroup === 'events' ? html`
                <fx-events slot="events" class="vertical flex overflow-h h100" .item=${this.bsSelected} .base=${this}></fx-events>
                ${this.addNotebook ? html`
                    <fx-jupyter id="add-info" slot="add-info" class="vertical flex overflow h100" .notebook=${this.addNotebook} .base=${this} showBorder></fx-jupyter>
                ` : ''}
            ` : ''}
            ${this.main?.activeTabGroup === 'hls' ? html`
                <fx-hls-day slot="hls-day" class="vertical flex overflow-h h100" .item=${this.isItem} .base=${this}></fx-hls-day>
                <fx-hls-total slot="hls-total" class="vertical flex overflow-h h100" .item=${this.isItem} .base=${this}></fx-hls-total>
             ` : ''}
        `
    }
    render() {
        return html`
                <fx-main id=${this.id + '-main'} class="w100 h100 relative" hide=${this.hideDBS ? "fbr" : "fb"} overlay='r' .btnsMT=${$btnsMT} minL=280 minR=162 ?needSave=${this.needSave} .tabs=${this.tabs} tabsWidth=100 .base=${this}>
                    <label slot="top">${this.fxItems?.items?.[0]?.label || this.dbLocal?.name || 'fx-base'}</label>
                    ${this.leftPanel}
                    ${this.mainPanel}   
                    ${this.rightPanel}
                </fx-main>
                <div class="loader" ?hidden=${!this.showLoader}></div>
            `
    }

    async save() {
        if (!this.needSave) return;
        this.tree?.storeTreeState(null, true);
        const map = BS_ITEM.changesMap,
            _keys = Array.from(map.keys()),
            db = this.dbLocal;

        let keys = [],
            rows = [],
            needReboot = false;

        const ops = ['file', 'hlsDay', 'info', 'eventinfo', 'money', 'moneyTotal', 'fields'];
        this.fxToDelete.map(i => {
            keys.add(i._id);
            ops.map(j => {
                keys.add(j + ':' + i._id);
            })
        })
        this.toDeleteKeys ||= [];
        this.toDeleteKeys.map(i => {
            keys.add(i);
        })
        this.toDeleteKeys = [];
        this.toSaveAttachments ||= {};
        await Object.values(this.toSaveAttachments).map(async attachment => {
            let item;
            try {
                item = await db.get(attachment._id)
            } catch (error) { }
            if (item?._rev)
                attachment._rev = item._rev;
            await db.put(attachment);
        })
        this.toSaveAttachments = {};

        if (keys.length) {
            let _keys = [];
            keys.map(i => {
                _keys.add(i);
                ops.map(j => {
                    _keys.add(j + ':' + i);
                })
            })
            keys.add(..._keys);
            keys = keys.filter(i => i);
            // const allDocs = await this.dbLocal.allDocs({ keys, include_docs: true });
            //c onst res = await keys.map(async i => {
            //const allDocs = await db.allDocs({ include_docs: false, startkey: i, endkey: i + '\ufff0' });
            const allDocs = await this.dbLocal.allDocs({ keys, include_docs: false });
            allDocs.rows.map(_i => {
                if (_i.value?.rev) {

                    let doc = { _id: _i.id, _rev: _i.value.rev, _deleted: true };
                    rows.add(doc);
                }
            })
            // })
            // await Promise.all(res);
            needReboot = true;
            // console.log(rows);
        }
        rows = rows.filter(i => i);
        if (rows.length) await this.dbLocal.bulkDocs(rows);

        keys = [];
        rows = [];
        _keys.map(async i => {
            keys.add(i);
        })
        if (keys.length) {
            keys = keys.filter(i => i);
            const allDocs = await this.dbLocal.allDocs({ keys, include_docs: false });
            allDocs.rows.map(i => {
                let doc = map.get(i.key)?.doc;
                // doc = this.lzsInfo && doc?.type === 'info' ? map.get(i.key)?.lzsDoc : doc; // ToDo ???
                if (doc) {
                    if (i.value?.rev)
                        doc._rev = i.value.rev;
                    rows.add(doc);
                }
            })
            rows = rows.filter(i => i);
            if (rows.length) await db.bulkDocs(rows);
        }
        map.clear();

        if (this.fxChanged) {
            this.fxChanged = false;
            this.fxAll.map(i => {
                delete i.expanded;
                delete i.checked;
            })
            const tree = await db.get(this.fxId);
            this.fxItems._rev = tree._rev;

            await db.put(this.fxItems);
        }
        if (this.attachments?.length) {
            this.attachments.map(async attachment => {
                let i;
                try {
                    i = await db.get(attachment._id)
                } catch (error) { }
                if (i?._rev)
                    attachment._rev = i._rev;
                await db.put(attachment)
                this.attachments = undefined;
                this.$update();
            })
        }

        if (needReboot) {
            this.async(() => {
                document.location.reload();
            })
        }
        await this.tree?.restoreTreeState();
        this.async(() => {
            this.fxChanged = false;
            BS_ITEM.changesMap.clear();
            this.$update();
        }, 100)
        this.$update();
    }

    async getParents(selected = this.fxSelected) {
        if (!selected) return;
        const type = selected.is;
        if (type === 'man' || type === 'woman') {
            if (selected.father) {
                selected.father = await this.getBsItem(selected.doc.father, true);
                selected.father ||= { label: '... Родитель не найден ...' };
            }
            if (selected.mother) {
                selected.mother = await this.getBsItem(selected.doc.mother, true);
                selected.mother ||= { label: '... Родитель не найден ...' };
            }
        }
    }
    async getSpouses(selected = this.bsSelected) {
        if (!selected) return;
        const type = selected.is;
        if (type === 'man' || type === 'woman') {
            if (Object.keys(selected.doc?.spouses || {}).length) {
                selected.spouses = {};
                Object.values(selected.doc.spouses).map(async i => {
                    let item = await this.getBsItem(i._id, true);
                    item ||= { label: '... Супруг(а) не найден(а) ...' };
                    // if (i.isCurrent) item.isCurrent = i.isCurrent;
                    if (i.startWedding) item.startWedding = i.startWedding;
                    if (i.startInCalendar) item.startInCalendar = i.startInCalendar;
                    if (i.endWedding) item.endWedding = i.endWedding;
                    if (i.endInCalendar) item.endCalendar = i.endInCalendar;
                    selected.spouses[i._id] = item;
                })
            }
        }
    }
    async getChildren(selected = this.bsSelected) {
        if (!selected) return;
        const type = selected.is;
        if (type === 'man' || type === 'woman') {
            this.bsAllPersons ||= await this.getBsFlat('man, woman');
            selected.children = [];
            Object.values(this.bsAllPersons).map(i => {
                if (selected._id === i.doc.father || selected._id === i.doc.mother) {
                    selected.children.push(i);
                }
            })
        }
    }

    async showSets(e) {
        let res = { inside: true };
        let fxChanged = true;
        const run = async (e, item) => {
            let id = item.id,
                spl = (item.icon2 || item.icon).split(':'),
                icon = spl[0] + (spl[1] ? ':' + spl[1] : '');
            // console.log(item);
            if (id === 'deleteIcon') {
                delete this.fxSelected.icon;
                delete this.fxSelected.iconSize;
                this.fxChanged = fxChanged;
            }
            else if (id === 'iconName') {
                this.fxSelected.icon = e.target.value;
                this.fxChanged = fxChanged;
            }
            else if (id === 'selectIcon') {
                let res = await FX.show('dropdown', '/icons/icons.js', { isSelectIcon: true }, {}, 'fx', 'fx');
                if (res?.detail?.res) {
                    let spl = res.detail.res.split(':'),
                        icon = spl[0] + (spl[1] ? ':' + spl[1] : '');
                    this.fxSelected.icon = icon;
                    this.fxChanged = fxChanged;
                }
            }
            else if (id === 'sizeIcon') {
                this.fxSelected.iconSize = e.target.value;
                this.fxChanged = fxChanged;
            }
            else if (id === 'hideInfo') {
                this.fxSelected.hideInfo = item.value;
                this.fxChanged = fxChanged;
                this.tree.selectById(this.fxSelected._id);
            }
            else if (id === 'ifCalendar') {
                this.fxSelected.if ||= {};
                this.fxSelected.if.ifCalendar = item.value;
                if (!item.value && icon === this.fxSelected.icon)
                    delete this.fxSelected.icon;
                else
                    this.fxSelected.icon ||= icon;
                this.fxChanged = fxChanged;
            }
            else if (id === 'ifEvents') {
                this.fxSelected.if ||= {};
                this.fxSelected.if.ifEvents = item.value;
                if (!item.value && icon === this.fxSelected.icon)
                    delete this.fxSelected.icon;
                else
                    this.fxSelected.icon ||= icon;
                this.fxChanged = fxChanged;
            }
            else if (id === 'deleteIs') {
                delete this.fxSelected.is;
                delete this.fxSelected.icon;
                delete this.fxSelected.iconSize;
                this.fxChanged = fxChanged;
            }
            else if (id.startsWith('is-')) {
                id = id.split('-')[1];
                if (item.value) {
                    this.fxSelected.is = id;
                    this.fxSelected.icon = icon;
                }
                else {
                    delete this.fxSelected.is;
                    delete this.fxSelected.icon;
                    delete this.fxSelected.iconSize;
                }
                this.fxChanged = fxChanged;
            }
            else if (id === 'showFX') {
                // FX.IO(this.fxSelected, { expert: true });
                FX.showJSON(this.fxSelected, 'fx-item ' + this.fxSelected.label);
            }
            else if (id === 'showBS') {
                // FX.IO(this.bsSelected, { expert: true });
                FX.showJSON(this.bsSelected, 'bs-item ' + this.bsSelected.label);
            }
            else if (id === 'inside') {
                res.inside = item.value;
            }
            else if (id.startsWith('add-cell-')) {
                id = id.split('-')[2];
                res.type = id;
                res.icon = icon;
                this.on_addItem('', res);
                this.fxChanged = fxChanged;
            }
            else if (id.startsWith('add-type-')) {
                id = id.split('-')[2];
                res.is = id;
                res.icon = icon;
                res.type = 'link';
                this.on_addItem('', res);
                this.fxChanged = fxChanged;
            }
            else if (id === 'share-selected') {
                console.log(id)
            }
            else if (id === 'share-db') {
                let name = this.dbLocal.name,
                    lastLZS = FX.ulid(),
                    pr = this.dbPrefix,
                    href = location.href.split('#')[0],
                    dbUrl = this.dbs?.dbUrl || this.dbUrl,
                    hideDBS = true,
                    tree = this.tree._updateSaves(),
                    base = this._updateSaves(),
                    main = this.main._updateSaves(),
                    tabs = this.main.fxTabs._updateSaves(),
                    lzs = JSON.stringify({ name, dbUrl, pr, hideDBS, tree, base, main, tabs, lastLZS });
                lzs = FX.jcuri(lzs);
                href += '#pr=' + pr + '&lzs=' + lzs;
                window.open(href, '_blank')
            }
            else if (id.startsWith('adds')) {
                this[id](res.inside);
            }
            else if (id === 'addCalendar') {
                res.type = 'link';
                res.icon = icon;
                res.if = { ifCalendar: true };
                this.on_addItem('', res);
                this.fxChanged = fxChanged;
            }
            this.async(() => {
                if (this.isShowTreeSets)
                    this.$fire('on_itemSelected', { item: this.dataTreeSets(), label: this.shortLabel, type: 'tree-sets' });
            }, 20)
            this.$update();
        }
        const item = this.dataTreeSets = () => [
            {
                id: 'addInfo', icon: 'flat-color-icons:data-recovery:28', label: 'add', subLabel: 'добавить', expanded: true, items: [
                    { id: 'inside', icon: 'fc:parallel-tasks:28', label: 'add inside', subLabel: 'добавлять внутрь', value: true, is: 'checkbox', run },
                    {
                        id: 'add-cell-link', icon: 'flat-color-icons:multiple-smartphones:28', label: 'add info', subLabel: 'добавить link', value: 'link', is: 'button', run, items: [
                            // { id: 'add-cell-link', icon: 'fc:survey:28', label: 'link', value: 'link', is: 'button', run },
                            { id: 'add-cell-html', icon: 'vscode-icons:file-type-html:28', label: 'html', value: 'html', is: 'button', run },
                            { id: 'add-cell-cherry-md', icon: 'emojione-v1:cherries:28', label: 'cherry-md', value: 'cherry-md', is: 'button', run },
                            { id: 'add-cell-galleries', icon: 'fc:multiple-cameras:28', label: 'galleries', value: 'galleries', is: 'button', run },
                            { id: 'add-cell-todo', icon: 'fc:todo-list:28', label: 'to-do', value: 'to-do', is: 'button', run },

                            {
                                id: 'add-cell-code', icon: 'devicon:vscode:28', label: 'code', value: 'code', is: 'button', run, items: [
                                    { id: 'add-cell-executable', icon: 'devicon:reactnative:28', label: 'executable', value: 'executable', is: 'button', run },
                                    { id: 'add-cell-diff', icon: 'flat-color-icons:process:28', label: 'diff', value: 'diff', is: 'button', run },
                                ]
                            },
                            {
                                id: 'add-cell-txt', icon: 'devicon-original:apachespark:28', icon2: 'flat-color-icons:kindle:28', label: 'special', value: 'txt', is: 'button', run, items: [
                                    { id: 'add-cell-svg', icon: 'devicon-original:apachespark:28', label: 'svg', value: 'svg', is: 'button', run },
                                    { id: 'add-cell-excalidraw', icon: 'devicon-original:gitbook:28', label: 'excalidraw', value: 'excalidraw', is: 'button', run },
                                    { id: 'add-cell-money', icon: 'fc:currency-exchange:28', label: 'money', value: 'money', is: 'button', run },
                                    { id: 'add-cell-family-tree', icon: 'fc:conference-call:28', label: 'family-tree', value: 'family-tree', is: 'button', run },
                                ]
                            },
                            {
                                id: 'add-cell-table', icon: 'flat-color-icons:view-details:28', label: 'table', value: 'table', is: 'button', run, items: [
                                    { id: 'add-cell-jspreadsheet', icon: 'fc:grid:28', label: 'jspreadsheet', value: 'jspreadsheet', is: 'button', run },
                                    { id: 'add-cell-spreadsheet', icon: 'fc:data-sheet:28', label: 'spreadsheet', value: 'spreadsheet', is: 'button', run },
                                ]
                            }

                        ]
                    },
                    {
                        id: 'add types', icon: 'fluent-emoji-flat:bookmark-tabs:28', label: 'add types', subLabel: 'добавить тип', items: [
                            {
                                id: 'addCalendar', icon: 'flat-color-icons:conference-call:28', icon: 'flat-color-icons:planner:28', label: 'persona', subLabel: 'персоны', value: 'add calendar', is: 'button', run, items: [
                                    { id: 'add-calendar', icon: 'flat-color-icons:businessman:28', label: 'man', subLabel: 'мужчина', value: 'man', is: 'button', run },
                                    { id: 'add-type-woman', icon: 'flat-color-icons:businesswoman:28', label: 'woman', subLabel: 'женщина', value: 'woman', is: 'button', run },
                                    { id: 'addsParents', icon: 'fx:close:28', svg: $icons.parents, label: 'parents', subLabel: 'родители', value: 'parents', is: 'button', run },
                                    { id: 'addsCouples', icon: 'fx:close:28', svg: $icons.weddingCouple, label: 'couples', subLabel: 'семейная пара', value: 'couples', is: 'button', run }
                                ]
                            },
                            {
                                id: 'add-type-hlsDay', icon: 'flat-color-icons:sports-mode:28', label: 'HLS day', subLabel: 'ЗОЖ по дням', value: 'HLS day', is: 'button', run, items: [
                                    { id: 'add-type-hlsTotal', icon: 'flat-color-icons:combo-chart:28', label: 'HLS total', subLabel: 'ЗОЖ итого', value: 'HLS total', is: 'button', run },
                                ]
                            },
                            {
                                id: 'add-type-money', icon: 'fc:currency-exchange:28', label: 'money day', subLabel: 'деньги за месяц', value: 'money day', is: 'button', run, items: [
                                    { id: 'add-type-moneyTotal', icon: 'noto-v1:money-bag:28', label: 'money total', subLabel: 'деньги итого', value: 'money total', is: 'button', run },
                                ]
                            },
                        ],
                    },


                ]
            },
            {
                id: 'set is', icon: 'fluent-emoji-flat:bookmark-tabs:28', label: 'set is - type', subLabel: 'установить / сменить тип', expanded: false, items: [
                    { id: 'deleteIs', icon: 'fx:close:28', fill: 'red', label: 'delete type', subLabel: 'удалить тип', value: 'delete type', is: 'button', run },
                    {
                        id: 'isPersona', icon: 'flat-color-icons:conference-call:28', label: 'persona', subLabel: 'персоны', items: [
                            { id: 'is-man', icon: 'flat-color-icons:businessman:28', label: 'man', subLabel: 'мужчина', value: this.fxSelected.is === 'man', is: 'checkbox', run },
                            { id: 'is-woman', icon: 'flat-color-icons:businesswoman:28', label: 'woman', subLabel: 'женщина', value: this.fxSelected.is === 'woman', is: 'checkbox', run },
                        ]
                    },
                    {
                        id: 'isHLS', icon: 'twemoji:sports-medal:28', label: 'health', subLabel: 'ЗОЖ', items: [
                            { id: 'is-hlsTotal', icon: 'flat-color-icons:combo-chart:28', label: 'HLS total', subLabel: 'ЗОЖ итого', value: this.fxSelected.is === 'hlsTotal', is: 'checkbox', run },
                            { id: 'is-hlsDay', icon: 'flat-color-icons:sports-mode:28', label: 'HLS day', subLabel: 'ЗОЖ по дням', value: this.fxSelected.is === 'hlsDay', is: 'checkbox', run },
                        ]
                    },
                    {
                        id: 'isMoney', icon: 'fluent-emoji-flat:heavy-dollar-sign:28', label: 'money', subLabel: 'деньги', items: [
                            { id: 'is-moneyTotal', icon: 'noto-v1:money-bag:28', label: 'money total', subLabel: 'деньги итого', value: this.fxSelected.is === 'moneyTotal', is: 'checkbox', run },
                            { id: 'is-money', icon: 'fc:currency-exchange:28', label: 'money day', subLabel: 'деньги по месяцам', value: this.fxSelected.is === 'money', is: 'checkbox', run },
                        ]
                    },
                ]
            },
            {
                id: 'ifShowHide', icon: 'flat-color-icons:search:28', label: 'if - show / hide', subLabel: 'показать / скрыть', expanded: false, items: [
                    { id: 'hideInfo', icon: 'flat-color-icons:about:28', label: 'hide info', subLabel: 'скрыть инфо таб', value: this.fxSelected.hideInfo, is: 'checkbox', run },
                    { id: 'ifCalendar', icon: 'flat-color-icons:planner:28', label: 'calendar', subLabel: 'показывать календарь', value: this.fxSelected.if?.ifCalendar, is: 'checkbox', run },
                    { id: 'ifEvents', icon: 'flat-color-icons:overtime:28', label: 'events', subLabel: 'показывать события', value: this.fxSelected.if?.ifEvents, is: 'checkbox', run },
                ]
            },
            {
                id: 'setsIcon', icon: 'flat-color-icons:stack-of-photos:28', label: 'icon', subLabel: 'действия с иконками', expanded: false, items: [
                    { id: 'iconName', icon: this.fxSelected.icon + ':28', label: 'icon name', subLabel: 'имя иконки', value: this.fxSelected.icon, _value: 'icon', run },
                    { id: 'selectIcon', icon: 'flat-color-icons:gallery:28', label: 'set icon', value: 'select icon', is: 'button', run },
                    { id: 'sizeIcon', icon: 'flat-color-icons:edit-image:28', label: 'icon size', subLabel: 'размер иконки', value: this.fxSelected.iconSize || 24, type: 'number', run },
                    { id: 'deleteIcon', icon: 'fx:close:28', fill: 'red', label: 'delete icon', value: 'delete icon', is: 'button', run },
                ]
            },
            {
                id: 'advancedSet', icon: 'flat-color-icons:graduation-cap:28', label: 'advanced', subLabel: 'расширенные настройки', expanded: false, items: [
                    {
                        id: 'shareItem', icon: 'flat-color-icons:share:28', label: 'share', subLabel: 'поделиться', items: [
                            // { id: 'share-selected', icon: 'flat-color-icons:link:28', label: 'share selected', value: 'selected item', is: 'button', run },
                            { id: 'share-db', icon: 'flat-color-icons:database:28', label: 'share db', subLabel: 'в новой вкладке', value: 'share db', is: 'button', run },
                        ]
                    },
                    {
                        id: 'showCode', icon: 'fluent-color:code-block-24:28', label: 'code', subLabel: 'служебные данные', items: [
                            { id: 'showFX', icon: 'vscode-icons:file-type-light-json:28', label: 'fxSelected', value: 'view FX', is: 'button', run },
                            // { id: 'showBS', icon: 'vscode-icons:file-type-light-json:28', label: 'bsSelected', value: 'view BS', is: 'button', run },
                        ]
                    },
                ]
            },
        ]
        this.isShowTreeSets = true;
        await FX.showGrid({ type: 'tree-sets', id: this.id + '-tree-sets', item: item(), rowHeight: 32, hideSelected: true }, { id: this.id + '-tree-sets-dd', label: this.shortLabel, parent: e.target, intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowTreeSets = false;
        return;
    }
    async showAddItem(_e) {
        const selected = this.fxSelected,
            res = { inside: true };
        const run = async (e, item) => {
            let id = item.id,
                spl = item.icon?.split(':'),
                icon = spl?.[0] + (spl?.[1] ? ':' + spl?.[1] : '');
            if (id === 'sbutton') {
                this.showAddItemSelectButton = item.value;
                // console.log(this.showAddItemSelectButton);
            }
            else if (id === 'add-sbutton') {
                // console.log(this.showAddItemSelectButton);
                if (this.showAddItemSelectButton === 'сын')
                    this.addsBoy(res.inside);
                else if (this.showAddItemSelectButton === 'дочь')
                    this.addsGirl(res.inside);
                else if (this.showAddItemSelectButton === 'супруг(а)')
                    this.addsCouples(res.inside, this.bsSelected.doc);
                else if (this.showAddItemSelectButton === 'отец' || this.showAddItemSelectButton === 'мать') {
                    let type = this.showAddItemSelectButton === 'отец' ? 'man' : this.showAddItemSelectButton === 'мать' ? 'woman' : '';
                    if (type)
                        this.addsParents(res.inside, type);
                }
            }
            else if (id === 'inside') {
                this.showAddItemInside = res.inside = item.value;
            }
            else if (id.startsWith('adds')) {
                this[id](res.inside);
            }
            else {
                res.value = item.id;
                res.icon = icon;
                res.noAddInfo = true;
                let persona = this.on_addItem('', res);
                this.updatePersons(persona);
                this.fxChanged = true;
                // FX.closeForm('base-add-item');
            }
        }
        const item = this.dataAddItem = () => [
            { id: 'inside', icon: 'fc:parallel-tasks:28', label: 'add inside', subLabel: 'добавлять внутрь', value: this.showAddItemInside, is: 'checkbox', run },
            { id: 'man', icon: 'flat-color-icons:businessman:28', label: 'man', subLabel: 'добавить мужчину', value: 'мужчина', is: 'button', run },
            { id: 'woman', icon: 'flat-color-icons:businesswoman:28', label: 'woman', subLabel: 'добавить женщину', value: 'женщина', is: 'button', run },
            { id: 'addsParents', icon: 'fx:dots:28', svg: $icons.parents, label: 'parents', subLabel: 'добавить родителей', value: 'родители', is: 'button', run },
            { id: 'addsCouples', icon: 'fx:dots:28', svg: $icons.weddingCouple, label: 'couples', subLabel: 'семейную пару', value: 'семейная пара', is: 'button', run },
            { id: 'add-sbutton', icon: 'bubbles:family-child-outline:28', fill: 'violet', label: 'add selected', subLabel: 'сын, дочь, муж, жена ...', value: this.showAddItemSelectButton || '', is: 'sbutton', options: ['', 'сын', 'дочь', 'супруг(а)', 'отец', 'мать'], run },
            { id: 'calendar', icon: 'flat-color-icons:planner:28', label: 'calendar', subLabel: 'добавить календарь', value: 'календарь', is: 'button', run },
            { id: 'info', icon: 'flat-color-icons:multiple-smartphones:28', label: 'info link', subLabel: 'добавить инфо', value: 'инфо', is: 'button', run },
        ]
        this.isShowAddItem = true;
        this.showAddItemInside = true;
        this.showAddItemSelectButton ||= '';
        await FX.showGrid({ type: 'add-item', id: this.id + '-add-item', item: item(), rowHeight: 32, hideSelected: true }, { id: this.id + '-add-item-dd', label: this.shortLabel, intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowAddItem = false;
    }
    async addsParents(inside, type) {
        let selected = this.fxSelected;
        if (selected.is === 'man' || selected.is === 'woman') {
            let man = { inside };
            let woman = { inside };
            if (!type || type === 'man') {
                man.value = 'man';
                man.icon = 'flat-color-icons:businessman';
                man.noAddInfo = true;
                man = await this.on_addItem('', man);
                man.label = 'Отец';
                selected.father = man._id;
                man.spouses ||= {};
                if (type && selected.mother) {
                    man.spouses[selected.mother] = { _id: selected.mother };
                    let mother = await this.getBsItem(selected.mother, true);
                    mother.spouses ||= {};
                    mother.spouses[man._id] = { _id: man._id };
                }
            }
            if (!type || type === 'woman') {
                woman.value = 'woman';
                woman.icon = 'flat-color-icons:businesswoman';
                woman.noAddInfo = true;
                woman = await this.on_addItem('', woman);
                woman.label = 'Мать';
                selected.mother = woman._id;
                woman.spouses ||= {};
                if (type && selected.father) {
                    woman.spouses[selected.father] = { _id: selected.father };
                    let father = await this.getBsItem(selected.father, true);
                    father.spouses ||= {};
                    father.spouses[woman._id] = { _id: woman._id };
                }
            }
            if (!type) {
                man.spouses[woman._id] = { _id: woman._id };
                woman.spouses[man._id] = { _id: man._id };
            }

            this.updatePersons(man, woman);
        }
    }
    async addsCouples(inside, selected) {
        if (!selected || selected.is === 'man' || selected.is === 'woman') {
            let man = { inside };
            let woman = { inside };
            if (selected?.is === 'man') {
                man = selected;
            } else {
                man.value = 'man';
                man.icon = 'flat-color-icons:businessman';
                man.noAddInfo = true;
                man = await this.on_addItem('', man);
                man.label = 'Муж';
            }
            man.spouses ||= {};
            if (selected?.is === 'woman') {
                woman = selected;
            } else {
                woman.value = 'woman';
                woman.icon = 'flat-color-icons:businesswoman';
                woman.noAddInfo = true;
                woman = await this.on_addItem('', woman);
                woman.label = 'Жена';
            }
            man.spouses[woman._id] = { _id: woman._id };
            woman.spouses ||= {};
            woman.spouses[man._id] = { _id: man._id };
            this.updatePersons(man, woman);
        }
    }
    async addsBoy(inside) {
        if (this.fxSelected.is === 'man' || this.fxSelected.is === 'woman') {
            let boy = { inside };
            boy.value = 'man';
            boy.icon = 'flat-color-icons:businessman';
            boy.noAddInfo = true;
            boy = await this.on_addItem('', boy);
            boy.label = 'Сын';
            if (this.fxSelected.is === 'man') {
                boy.father = this.fxSelected._id;
            } else {
                boy.mother = this.fxSelected._id;
            }
            this.updatePersons(boy);
        }
    }
    async addsGirl(inside) {
        if (this.fxSelected.is !== 'man' || this.fxSelected.is !== 'woman') {
            let girl = { inside };
            girl.value = 'woman';
            girl.icon = 'flat-color-icons:businesswoman';
            girl.noAddInfo = true;
            girl = await this.on_addItem('', girl);
            girl.label = 'Дочь';
            if (this.fxSelected.is === 'man') {
                girl.father = this.fxSelected._id;
            } else {
                girl.mother = this.fxSelected._id;
            }
            this.updatePersons(girl);
        }
    }
    async updatePersons(p1, p2) {
        await this.getBsItem(p1._id, true, true);
        if (p2) await this.getBsItem(p2._id, true, true);
        await this.getParents();
        await this.getSpouses();
        await this.getChildren();
        this.fxChanged = true;
    }
    async getLZS(lzs) {
        lzs = FX.jduri(lzs);
        lzs = JSON.parse(lzs);
        if (!lzs?.name) return;
        this.hideDBS = lzs.hideDBS;
        let name = lzs.name,
            dbUrl = lzs.dbUrl;
        this.dbUrl = dbUrl;
        this.$update();
        let dbs = await FX.purix(name, dbUrl);
        this.dbLocal = dbs.local;
        this.dbRemote = dbs.remote;

        await this.dbLocal.replicate.from(this.dbRemote).on('complete', result => {
            // console.log(result);
            this.async(() => this.showLoader = false, 100);
            this.syncHandler = this.dbLocal.sync(this.dbRemote, { live: true, retry: true })
                .on('change', change => { /*console.log('change')*/ })
                .on('paused', paused => { /*console.log('paused')*/ })
                .on('error', error => { /*console.log('sync error')*/ });
            this.on_dbSelected();
            this.restoreState(lzs);
        }).on('error', error => { console.log('replicate error - ', error) });

    }
    restoreState(lzs) {
        this.async(() => {
            this._updateSaves();
            if (this.lastLZS === lzs.lastLZS) return;
            this.lastLZS = lzs.lastLZS;
            Object.keys(lzs.main || {}).forEach(key => this.main[key] = lzs.main[key]);
            this.main._updateSaves();
            this.async(() => {
                this.main.fxTabs.selectTab(lzs.tabs?.activeTab);
                Object.keys(lzs.tree || {}).forEach(key => this.tree[key] = lzs.tree[key]);
                this.tree.restoreTreeState();
                this.$update();
            }, 100)
        }, 100)
    }
}

customElements.define('fx-base', FxBase);
